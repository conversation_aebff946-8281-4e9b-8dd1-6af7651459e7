#!/usr/bin/env python3
"""
Simple test to check PDF libraries
"""

import os
import sys

def test_libraries():
    print("Testing PDF libraries...")
    
    try:
        import fitz
        print("✅ PyMuPDF (fitz) imported successfully")
    except Exception as e:
        print(f"❌ PyMuPDF import failed: {e}")
        return False
    
    try:
        import pdfplumber
        print("✅ pdfplumber imported successfully")
    except Exception as e:
        print(f"❌ pdfplumber import failed: {e}")
        return False
    
    try:
        import camelot
        print("✅ camelot imported successfully")
    except Exception as e:
        print(f"❌ camelot import failed: {e}")
        return False
    
    return True

def test_pdf_file():
    pdf_path = "ملف كامل تعريفي موج لتنظيم المعارض والمؤتمرات.pdf"
    
    print(f"\nTesting PDF file: {pdf_path}")
    print(f"File exists: {os.path.exists(pdf_path)}")
    
    if not os.path.exists(pdf_path):
        print("PDF file not found!")
        return False
    
    try:
        import fitz
        doc = fitz.open(pdf_path)
        print(f"✅ PDF opened successfully")
        print(f"📄 Total pages: {len(doc)}")
        print(f"📊 Metadata: {doc.metadata}")
        doc.close()
        return True
    except Exception as e:
        print(f"❌ Error opening PDF: {e}")
        return False

if __name__ == "__main__":
    print("Starting simple tests...")
    
    if test_libraries():
        print("\n✅ All libraries imported successfully")
        if test_pdf_file():
            print("\n✅ PDF file test passed")
        else:
            print("\n❌ PDF file test failed")
    else:
        print("\n❌ Library import test failed")
