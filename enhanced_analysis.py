#!/usr/bin/env python3
"""
Enhanced analysis script with OCR capabilities for image-based PDFs
"""

import os
import json
from pathlib import Path

def analyze_extracted_data():
    """Analyze the extracted data and provide insights"""
    
    output_dir = "extracted_data_ملف كامل تعريفي موج لتنظيم المعارض والمؤتمرات"
    
    if not os.path.exists(output_dir):
        print("❌ Extracted data directory not found!")
        return
    
    print("📊 ENHANCED ANALYSIS OF EXTRACTED PDF DATA")
    print("=" * 50)
    
    # Load and analyze metadata
    try:
        with open(os.path.join(output_dir, "metadata.json"), "r", encoding="utf-8") as f:
            metadata = json.load(f)
        
        print("\n📄 PDF METADATA:")
        print(f"   • File Size: {metadata['file_info']['file_size_mb']} MB")
        print(f"   • Total Pages: {metadata['file_info']['page_count']}")
        print(f"   • Created: {metadata['basic_metadata']['creationDate']}")
        print(f"   • Creator: {metadata['basic_metadata']['creator']}")
        print(f"   • Producer: {metadata['basic_metadata']['producer']}")
        print(f"   • Encrypted: {metadata['security']['is_encrypted']}")
        
    except Exception as e:
        print(f"❌ Error reading metadata: {e}")
    
    # Load and analyze structure
    try:
        with open(os.path.join(output_dir, "structure_analysis.json"), "r", encoding="utf-8") as f:
            structure = json.load(f)
        
        print(f"\n🏗️ PDF STRUCTURE:")
        print(f"   • Total Pages: {structure['total_pages']}")
        print(f"   • Has Text: {structure['has_text']}")
        print(f"   • Has Images: {structure['has_images']}")
        print(f"   • Has Tables: {structure['has_tables']}")
        print(f"   • Has Forms: {structure['has_forms']}")
        
        # Analyze page distribution
        pages_with_text_blocks = sum(1 for page in structure['page_analysis'] if page['text_blocks'] > 0)
        pages_with_images = sum(1 for page in structure['page_analysis'] if page['images'] > 0)
        
        print(f"   • Pages with text blocks: {pages_with_text_blocks}")
        print(f"   • Pages with images: {pages_with_images}")
        
    except Exception as e:
        print(f"❌ Error reading structure: {e}")
    
    # Load and analyze images
    try:
        with open(os.path.join(output_dir, "images_data.json"), "r", encoding="utf-8") as f:
            images_data = json.load(f)
        
        print(f"\n🖼️ IMAGES ANALYSIS:")
        print(f"   • Total Images: {images_data['summary']['total_images']}")
        
        if images_data['images']:
            # Analyze image properties
            total_size = sum(img['size_bytes'] for img in images_data['images'])
            avg_width = sum(img['width'] for img in images_data['images']) / len(images_data['images'])
            avg_height = sum(img['height'] for img in images_data['images']) / len(images_data['images'])
            
            print(f"   • Total Size: {total_size / (1024*1024):.2f} MB")
            print(f"   • Average Dimensions: {avg_width:.0f} x {avg_height:.0f} pixels")
            print(f"   • Color Space: {images_data['images'][0]['colorspace']}")
            
            # Find largest and smallest images
            largest = max(images_data['images'], key=lambda x: x['size_bytes'])
            smallest = min(images_data['images'], key=lambda x: x['size_bytes'])
            
            print(f"   • Largest Image: Page {largest['page']} ({largest['size_bytes']/(1024*1024):.2f} MB)")
            print(f"   • Smallest Image: Page {smallest['page']} ({smallest['size_bytes']/(1024*1024):.2f} MB)")
        
    except Exception as e:
        print(f"❌ Error reading images data: {e}")
    
    # Check if images directory exists and count files
    images_dir = os.path.join(output_dir, "images")
    if os.path.exists(images_dir):
        image_files = [f for f in os.listdir(images_dir) if f.endswith('.png')]
        print(f"   • Extracted Image Files: {len(image_files)}")
    
    # Analyze the nature of the PDF
    print(f"\n🔍 PDF TYPE ANALYSIS:")
    
    if structure['has_text'] == False and structure['has_images'] == True:
        print("   📋 This is an IMAGE-BASED PDF")
        print("   • Each page appears to be a scanned image or graphic")
        print("   • Text content is embedded within images")
        print("   • OCR (Optical Character Recognition) would be needed to extract text")
        print("   • This is typical of:")
        print("     - Scanned documents")
        print("     - Presentation slides saved as images")
        print("     - Graphic-heavy brochures or catalogs")
    
    # Provide recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    print("   1. 🔤 TEXT EXTRACTION:")
    print("      • Use OCR tools like Tesseract to extract text from images")
    print("      • Consider using cloud OCR services for better Arabic text recognition")
    print("      • Process images in batches for efficiency")
    
    print("   2. 📊 DATA ORGANIZATION:")
    print("      • Images are already extracted and organized by page")
    print("      • Each image is high-resolution (3508x2480 pixels)")
    print("      • Consider creating thumbnails for quick preview")
    
    print("   3. 🔍 CONTENT ANALYSIS:")
    print("      • Manual review of images may be needed to identify content types")
    print("      • Look for patterns in image sizes to identify different content sections")
    print("      • Consider using image classification to categorize content")
    
    print("   4. 📁 FILE MANAGEMENT:")
    print("      • Original PDF is 123.36 MB")
    print("      • Extracted images may take significant disk space")
    print("      • Consider compression for archival purposes")
    
    print(f"\n✅ EXTRACTION SUMMARY:")
    print(f"   • Successfully extracted {images_data['summary']['total_images']} high-quality images")
    print(f"   • All images are in PNG format with RGB color space")
    print(f"   • Images are organized by page number for easy reference")
    print(f"   • Metadata and structure information preserved")
    print(f"   • Ready for further processing (OCR, analysis, etc.)")

if __name__ == "__main__":
    analyze_extracted_data()
