#!/usr/bin/env python3
"""
Step-by-step PDF extraction to debug issues
"""

import os
import sys
import json
from pdf_extractor import PDFDataExtractor

def run_step_by_step():
    pdf_path = "ملف كامل تعريفي موج لتنظيم المعارض والمؤتمرات.pdf"
    
    print(f"Starting step-by-step extraction for: {pdf_path}")
    
    try:
        print("\n1. Creating extractor...")
        extractor = PDFDataExtractor(pdf_path)
        print(f"✅ Extractor created. Output directory: {extractor.output_dir}")
        
        print("\n2. Analyzing PDF structure...")
        structure = extractor.analyze_pdf_structure()
        if structure:
            print("✅ Structure analysis completed")
            print(f"   - Total pages: {structure['total_pages']}")
            print(f"   - Has text: {structure['has_text']}")
            print(f"   - Has images: {structure['has_images']}")
            print(f"   - Has tables: {structure['has_tables']}")
            print(f"   - Has forms: {structure['has_forms']}")
        else:
            print("❌ Structure analysis failed")
            return False
        
        print("\n3. Extracting metadata...")
        metadata = extractor.extract_metadata()
        if metadata:
            print("✅ Metadata extraction completed")
            print(f"   - File size: {metadata['file_info']['file_size_mb']} MB")
            print(f"   - PDF version: {metadata['file_info']['pdf_version']}")
        else:
            print("❌ Metadata extraction failed")
        
        print("\n4. Extracting text content...")
        text_data = extractor.extract_text_content()
        if text_data:
            print("✅ Text extraction completed")
            print(f"   - Pages with text: {len(text_data['pages'])}")
            print(f"   - Total characters: {len(text_data['full_text'])}")
        else:
            print("❌ Text extraction failed")
        
        print("\n5. Extracting images...")
        images_data = extractor.extract_images()
        if images_data:
            print("✅ Image extraction completed")
            print(f"   - Total images: {images_data['summary']['total_images']}")
            print(f"   - Pages with images: {images_data['summary']['pages_with_images']}")
        else:
            print("❌ Image extraction failed")
        
        print("\n6. Extracting tables...")
        tables_data = extractor.extract_tables()
        if tables_data:
            print("✅ Table extraction completed")
            print(f"   - Total tables: {tables_data['summary']['total_tables']}")
        else:
            print("❌ Table extraction failed")
        
        print("\n7. Extracting form fields...")
        forms_data = extractor.extract_form_fields()
        if forms_data:
            print("✅ Form extraction completed")
            print(f"   - Total fields: {forms_data['summary']['total_fields']}")
        else:
            print("❌ Form extraction failed")
        
        print("\n8. Generating summary report...")
        summary = extractor.generate_summary_report()
        if summary:
            print("✅ Summary report generated")
        else:
            print("❌ Summary report generation failed")
        
        print(f"\n🎉 All steps completed! Results saved in: {extractor.output_dir}")
        return True
        
    except Exception as e:
        print(f"❌ Error during extraction: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    run_step_by_step()
