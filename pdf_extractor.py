#!/usr/bin/env python3
"""
Comprehensive PDF Data Extractor
Extracts and organizes various types of data from PDF files including:
- Text content
- Tables
- Images
- Form fields
- Metadata
"""

import os
import json
import fitz  # PyMuPDF
import pdfplumber
import pandas as pd
from PIL import Image
import camelot
from pathlib import Path
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PDFDataExtractor:
    def __init__(self, pdf_path):
        self.pdf_path = pdf_path
        self.pdf_name = Path(pdf_path).stem
        self.output_dir = f"extracted_data_{self.pdf_name}"
        self.extraction_report = {
            "pdf_file": pdf_path,
            "extraction_date": datetime.now().isoformat(),
            "total_pages": 0,
            "extracted_data": {
                "text": {"pages": 0, "total_chars": 0},
                "tables": {"count": 0, "pages": []},
                "images": {"count": 0, "pages": []},
                "forms": {"fields": 0, "pages": []},
                "metadata": {}
            }
        }
        
        # Create output directory
        os.makedirs(self.output_dir, exist_ok=True)
        
    def analyze_pdf_structure(self):
        """Analyze the PDF structure and identify data types"""
        logger.info(f"Analyzing PDF structure: {self.pdf_path}")
        
        try:
            # Open with PyMuPDF for general analysis
            doc = fitz.open(self.pdf_path)
            self.extraction_report["total_pages"] = len(doc)
            
            structure_info = {
                "total_pages": len(doc),
                "has_text": False,
                "has_images": False,
                "has_forms": False,
                "has_tables": False,
                "page_analysis": []
            }
            
            for page_num in range(len(doc)):
                page = doc[page_num]
                page_info = {
                    "page": page_num + 1,
                    "text_blocks": len(page.get_text("dict")["blocks"]),
                    "images": len(page.get_images()),
                    "links": len(page.get_links()),
                    "annotations": len(list(page.annots())) if page.annots() else 0
                }
                
                # Check for text
                if page.get_text().strip():
                    structure_info["has_text"] = True
                
                # Check for images
                if page.get_images():
                    structure_info["has_images"] = True
                
                # Check for form fields
                if page.widgets():
                    structure_info["has_forms"] = True
                
                structure_info["page_analysis"].append(page_info)
            
            doc.close()
            
            # Check for tables using pdfplumber
            with pdfplumber.open(self.pdf_path) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    tables = page.find_tables()
                    if tables:
                        structure_info["has_tables"] = True
                        break
            
            # Save structure analysis
            with open(os.path.join(self.output_dir, "structure_analysis.json"), "w", encoding="utf-8") as f:
                json.dump(structure_info, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Structure analysis complete. Found: Text={structure_info['has_text']}, "
                       f"Images={structure_info['has_images']}, Tables={structure_info['has_tables']}, "
                       f"Forms={structure_info['has_forms']}")
            
            return structure_info
            
        except Exception as e:
            logger.error(f"Error analyzing PDF structure: {e}")
            return None
    
    def extract_text_content(self):
        """Extract all text content preserving formatting"""
        logger.info("Extracting text content...")
        
        try:
            text_data = {
                "full_text": "",
                "pages": [],
                "paragraphs": [],
                "headings": []
            }
            
            with pdfplumber.open(self.pdf_path) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    page_text = page.extract_text()
                    if page_text:
                        page_info = {
                            "page": page_num + 1,
                            "text": page_text,
                            "char_count": len(page_text)
                        }
                        text_data["pages"].append(page_info)
                        text_data["full_text"] += f"\n--- Page {page_num + 1} ---\n{page_text}\n"
                        
                        # Extract paragraphs (simple heuristic)
                        paragraphs = [p.strip() for p in page_text.split('\n\n') if p.strip()]
                        for para in paragraphs:
                            if len(para) > 50:  # Likely a paragraph
                                text_data["paragraphs"].append({
                                    "page": page_num + 1,
                                    "text": para
                                })
                            elif len(para) < 100 and para.isupper():  # Likely a heading
                                text_data["headings"].append({
                                    "page": page_num + 1,
                                    "text": para
                                })
            
            # Update report
            self.extraction_report["extracted_data"]["text"]["pages"] = len(text_data["pages"])
            self.extraction_report["extracted_data"]["text"]["total_chars"] = len(text_data["full_text"])
            
            # Save text data
            with open(os.path.join(self.output_dir, "text_content.json"), "w", encoding="utf-8") as f:
                json.dump(text_data, f, indent=2, ensure_ascii=False)
            
            # Save plain text file
            with open(os.path.join(self.output_dir, "full_text.txt"), "w", encoding="utf-8") as f:
                f.write(text_data["full_text"])
            
            logger.info(f"Text extraction complete. {len(text_data['pages'])} pages processed.")
            return text_data
            
        except Exception as e:
            logger.error(f"Error extracting text: {e}")
            return None

    def extract_tables(self):
        """Extract tables using multiple methods"""
        logger.info("Extracting tables...")

        try:
            tables_data = {
                "tables": [],
                "summary": {"total_tables": 0, "pages_with_tables": []}
            }

            # Method 1: Using pdfplumber
            with pdfplumber.open(self.pdf_path) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    tables = page.find_tables()
                    for table_idx, table in enumerate(tables):
                        try:
                            table_data = page.extract_table(table.bbox)
                            if table_data:
                                table_info = {
                                    "page": page_num + 1,
                                    "table_index": table_idx + 1,
                                    "method": "pdfplumber",
                                    "data": table_data,
                                    "rows": len(table_data),
                                    "columns": len(table_data[0]) if table_data else 0
                                }
                                tables_data["tables"].append(table_info)

                                if page_num + 1 not in tables_data["summary"]["pages_with_tables"]:
                                    tables_data["summary"]["pages_with_tables"].append(page_num + 1)
                        except Exception as e:
                            logger.warning(f"Error extracting table {table_idx + 1} on page {page_num + 1}: {e}")

            # Method 2: Using camelot (for more complex tables)
            try:
                camelot_tables = camelot.read_pdf(self.pdf_path, pages='all')
                for idx, table in enumerate(camelot_tables):
                    table_info = {
                        "page": table.page,
                        "table_index": idx + 1,
                        "method": "camelot",
                        "data": table.df.values.tolist(),
                        "rows": len(table.df),
                        "columns": len(table.df.columns),
                        "accuracy": table.accuracy if hasattr(table, 'accuracy') else None
                    }
                    tables_data["tables"].append(table_info)

                    if table.page not in tables_data["summary"]["pages_with_tables"]:
                        tables_data["summary"]["pages_with_tables"].append(table.page)
            except Exception as e:
                logger.warning(f"Camelot table extraction failed: {e}")

            tables_data["summary"]["total_tables"] = len(tables_data["tables"])

            # Update report
            self.extraction_report["extracted_data"]["tables"]["count"] = tables_data["summary"]["total_tables"]
            self.extraction_report["extracted_data"]["tables"]["pages"] = tables_data["summary"]["pages_with_tables"]

            # Save tables data
            with open(os.path.join(self.output_dir, "tables_data.json"), "w", encoding="utf-8") as f:
                json.dump(tables_data, f, indent=2, ensure_ascii=False)

            # Save individual tables as CSV
            tables_dir = os.path.join(self.output_dir, "tables_csv")
            os.makedirs(tables_dir, exist_ok=True)

            for idx, table in enumerate(tables_data["tables"]):
                try:
                    df = pd.DataFrame(table["data"])
                    csv_filename = f"table_page{table['page']}_index{table['table_index']}_{table['method']}.csv"
                    df.to_csv(os.path.join(tables_dir, csv_filename), index=False, encoding="utf-8")
                except Exception as e:
                    logger.warning(f"Error saving table {idx + 1} as CSV: {e}")

            logger.info(f"Table extraction complete. {tables_data['summary']['total_tables']} tables found.")
            return tables_data

        except Exception as e:
            logger.error(f"Error extracting tables: {e}")
            return None

    def extract_images(self):
        """Extract all images from the PDF"""
        logger.info("Extracting images...")

        try:
            images_data = {
                "images": [],
                "summary": {"total_images": 0, "pages_with_images": []}
            }

            doc = fitz.open(self.pdf_path)
            images_dir = os.path.join(self.output_dir, "images")
            os.makedirs(images_dir, exist_ok=True)

            for page_num in range(len(doc)):
                page = doc[page_num]
                image_list = page.get_images()

                for img_idx, img in enumerate(image_list):
                    try:
                        # Get image data
                        xref = img[0]
                        pix = fitz.Pixmap(doc, xref)

                        if pix.n - pix.alpha < 4:  # GRAY or RGB
                            img_filename = f"page{page_num + 1}_img{img_idx + 1}.png"
                            img_path = os.path.join(images_dir, img_filename)
                            pix.save(img_path)

                            image_info = {
                                "page": page_num + 1,
                                "image_index": img_idx + 1,
                                "filename": img_filename,
                                "width": pix.width,
                                "height": pix.height,
                                "colorspace": pix.colorspace.name if pix.colorspace else "Unknown",
                                "size_bytes": len(pix.tobytes())
                            }
                            images_data["images"].append(image_info)

                            if page_num + 1 not in images_data["summary"]["pages_with_images"]:
                                images_data["summary"]["pages_with_images"].append(page_num + 1)

                        pix = None  # Free memory

                    except Exception as e:
                        logger.warning(f"Error extracting image {img_idx + 1} on page {page_num + 1}: {e}")

            doc.close()

            images_data["summary"]["total_images"] = len(images_data["images"])

            # Update report
            self.extraction_report["extracted_data"]["images"]["count"] = images_data["summary"]["total_images"]
            self.extraction_report["extracted_data"]["images"]["pages"] = images_data["summary"]["pages_with_images"]

            # Save images data
            with open(os.path.join(self.output_dir, "images_data.json"), "w", encoding="utf-8") as f:
                json.dump(images_data, f, indent=2, ensure_ascii=False)

            logger.info(f"Image extraction complete. {images_data['summary']['total_images']} images found.")
            return images_data

        except Exception as e:
            logger.error(f"Error extracting images: {e}")
            return None

    def extract_form_fields(self):
        """Extract form fields and their values"""
        logger.info("Extracting form fields...")

        try:
            forms_data = {
                "form_fields": [],
                "summary": {"total_fields": 0, "pages_with_forms": []}
            }

            doc = fitz.open(self.pdf_path)

            for page_num in range(len(doc)):
                page = doc[page_num]
                widgets = page.widgets()

                if widgets:
                    for widget in widgets:
                        try:
                            field_info = {
                                "page": page_num + 1,
                                "field_name": widget.field_name,
                                "field_type": widget.field_type_string,
                                "field_value": widget.field_value,
                                "field_label": widget.field_label,
                                "rect": list(widget.rect),
                                "is_writable": not widget.field_flags & 1  # Check if read-only
                            }
                            forms_data["form_fields"].append(field_info)

                            if page_num + 1 not in forms_data["summary"]["pages_with_forms"]:
                                forms_data["summary"]["pages_with_forms"].append(page_num + 1)

                        except Exception as e:
                            logger.warning(f"Error extracting form field on page {page_num + 1}: {e}")

            doc.close()

            forms_data["summary"]["total_fields"] = len(forms_data["form_fields"])

            # Update report
            self.extraction_report["extracted_data"]["forms"]["fields"] = forms_data["summary"]["total_fields"]
            self.extraction_report["extracted_data"]["forms"]["pages"] = forms_data["summary"]["pages_with_forms"]

            # Save forms data
            with open(os.path.join(self.output_dir, "forms_data.json"), "w", encoding="utf-8") as f:
                json.dump(forms_data, f, indent=2, ensure_ascii=False)

            logger.info(f"Form extraction complete. {forms_data['summary']['total_fields']} fields found.")
            return forms_data

        except Exception as e:
            logger.error(f"Error extracting forms: {e}")
            return None

    def extract_metadata(self):
        """Extract PDF metadata"""
        logger.info("Extracting metadata...")

        try:
            doc = fitz.open(self.pdf_path)
            metadata = doc.metadata

            # Get additional information
            page_count = len(doc)
            file_size = os.path.getsize(self.pdf_path)

            metadata_info = {
                "basic_metadata": metadata,
                "file_info": {
                    "file_size_bytes": file_size,
                    "file_size_mb": round(file_size / (1024 * 1024), 2),
                    "page_count": page_count,
                    "pdf_version": doc.pdf_version() if hasattr(doc, 'pdf_version') else "Unknown"
                },
                "security": {
                    "is_encrypted": doc.is_encrypted,
                    "needs_pass": doc.needs_pass,
                    "permissions": doc.permissions if hasattr(doc, 'permissions') else None
                }
            }

            doc.close()

            # Update report
            self.extraction_report["extracted_data"]["metadata"] = metadata_info

            # Save metadata
            with open(os.path.join(self.output_dir, "metadata.json"), "w", encoding="utf-8") as f:
                json.dump(metadata_info, f, indent=2, ensure_ascii=False)

            logger.info("Metadata extraction complete.")
            return metadata_info

        except Exception as e:
            logger.error(f"Error extracting metadata: {e}")
            return None

    def generate_summary_report(self):
        """Generate a comprehensive summary report"""
        logger.info("Generating summary report...")

        try:
            # Save the extraction report
            with open(os.path.join(self.output_dir, "extraction_report.json"), "w", encoding="utf-8") as f:
                json.dump(self.extraction_report, f, indent=2, ensure_ascii=False)

            # Generate a human-readable summary
            summary_text = f"""
PDF DATA EXTRACTION SUMMARY
===========================

File: {self.extraction_report['pdf_file']}
Extraction Date: {self.extraction_report['extraction_date']}
Total Pages: {self.extraction_report['total_pages']}

EXTRACTED DATA SUMMARY:
----------------------

TEXT CONTENT:
- Pages with text: {self.extraction_report['extracted_data']['text']['pages']}
- Total characters: {self.extraction_report['extracted_data']['text']['total_chars']:,}

TABLES:
- Total tables found: {self.extraction_report['extracted_data']['tables']['count']}
- Pages with tables: {', '.join(map(str, self.extraction_report['extracted_data']['tables']['pages'])) if self.extraction_report['extracted_data']['tables']['pages'] else 'None'}

IMAGES:
- Total images found: {self.extraction_report['extracted_data']['images']['count']}
- Pages with images: {', '.join(map(str, self.extraction_report['extracted_data']['images']['pages'])) if self.extraction_report['extracted_data']['images']['pages'] else 'None'}

FORM FIELDS:
- Total form fields: {self.extraction_report['extracted_data']['forms']['fields']}
- Pages with forms: {', '.join(map(str, self.extraction_report['extracted_data']['forms']['pages'])) if self.extraction_report['extracted_data']['forms']['pages'] else 'None'}

OUTPUT STRUCTURE:
----------------
All extracted data has been organized in the '{self.output_dir}' directory:

├── structure_analysis.json     # PDF structure analysis
├── text_content.json          # All text content with page info
├── full_text.txt              # Plain text version
├── tables_data.json           # Table data and metadata
├── tables_csv/                # Individual tables as CSV files
├── images_data.json           # Image metadata
├── images/                    # Extracted images
├── forms_data.json            # Form fields and values
├── metadata.json              # PDF metadata and file info
├── extraction_report.json     # Complete extraction report
└── summary_report.txt         # This summary

RECOMMENDATIONS:
---------------
- Review the structure_analysis.json for detailed page-by-page breakdown
- Check individual CSV files in tables_csv/ for structured data
- Examine images/ directory for visual content
- Use the JSON files for programmatic access to extracted data
"""

            # Save summary report
            with open(os.path.join(self.output_dir, "summary_report.txt"), "w", encoding="utf-8") as f:
                f.write(summary_text)

            logger.info(f"Summary report generated in {self.output_dir}")
            return summary_text

        except Exception as e:
            logger.error(f"Error generating summary report: {e}")
            return None

    def extract_all_data(self):
        """Extract all types of data from the PDF"""
        logger.info(f"Starting comprehensive data extraction from: {self.pdf_path}")

        try:
            # Step 1: Analyze PDF structure
            structure = self.analyze_pdf_structure()
            if not structure:
                logger.error("Failed to analyze PDF structure")
                return False

            # Step 2: Extract metadata
            metadata = self.extract_metadata()

            # Step 3: Extract text content
            text_data = self.extract_text_content()

            # Step 4: Extract tables
            tables_data = self.extract_tables()

            # Step 5: Extract images
            images_data = self.extract_images()

            # Step 6: Extract form fields
            forms_data = self.extract_form_fields()

            # Step 7: Generate summary report
            summary = self.generate_summary_report()

            logger.info("Data extraction completed successfully!")
            logger.info(f"Results saved in: {self.output_dir}")

            return True

        except Exception as e:
            logger.error(f"Error during data extraction: {e}")
            return False


def main():
    """Main function to run the PDF extractor"""
    import sys

    if len(sys.argv) != 2:
        print("Usage: python pdf_extractor.py <path_to_pdf_file>")
        sys.exit(1)

    pdf_path = sys.argv[1]

    if not os.path.exists(pdf_path):
        print(f"Error: PDF file not found: {pdf_path}")
        sys.exit(1)

    if not pdf_path.lower().endswith('.pdf'):
        print(f"Error: File must be a PDF: {pdf_path}")
        sys.exit(1)

    # Create extractor and run extraction
    extractor = PDFDataExtractor(pdf_path)
    success = extractor.extract_all_data()

    if success:
        print(f"\n✅ PDF data extraction completed successfully!")
        print(f"📁 Results saved in: {extractor.output_dir}")
        print(f"📄 Check 'summary_report.txt' for a detailed overview")
    else:
        print("\n❌ PDF data extraction failed. Check the logs for details.")
        sys.exit(1)


if __name__ == "__main__":
    main()
