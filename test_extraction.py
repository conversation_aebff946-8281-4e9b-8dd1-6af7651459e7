#!/usr/bin/env python3
"""
Test script for PDF extraction
"""

import os
import sys
from pdf_extractor import PDFDataExtractor

def test_extraction():
    pdf_path = "ملف كامل تعريفي موج لتنظيم المعارض والمؤتمرات.pdf"
    
    print(f"Testing PDF extraction for: {pdf_path}")
    print(f"File exists: {os.path.exists(pdf_path)}")
    
    if not os.path.exists(pdf_path):
        print("PDF file not found!")
        return False
    
    try:
        print("Creating extractor...")
        extractor = PDFDataExtractor(pdf_path)
        
        print("Starting extraction...")
        success = extractor.extract_all_data()
        
        if success:
            print("✅ Extraction completed successfully!")
            print(f"📁 Results saved in: {extractor.output_dir}")
        else:
            print("❌ Extraction failed!")
            
        return success
        
    except Exception as e:
        print(f"Error during extraction: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_extraction()
